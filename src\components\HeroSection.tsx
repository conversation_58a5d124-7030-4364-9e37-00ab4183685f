import React, { useEffect, useRef, Children } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { ArrowRightIcon } from 'lucide-react';
const HeroSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  const backgroundParallax = {
    initial: {
      scale: 1.1
    },
    animate: {
      scale: 1,
      transition: {
        duration: 1.5,
        ease: 'easeOut'
      }
    }
  };
  return <motion.section className="relative w-full h-screen overflow-hidden bg-[#1e3a5f]" initial="initial" animate="animate">
      <motion.div className="absolute inset-0 z-0" variants={backgroundParallax}>
        <div className="absolute inset-0 bg-black/40 z-10" style={{
        backgroundImage: 'linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7))'
      }}></div>
        <div className="w-full h-full bg-cover bg-center" style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80')",
        backgroundPosition: 'center 30%'
      }}></div>
      </motion.div>
      <div className="absolute inset-0 z-20">
        <div className="container mx-auto h-full flex items-center px-4 md:px-8">
          <motion.div ref={ref} className="max-w-3xl" variants={containerVariants} initial="hidden" animate={controls}>
            <motion.div variants={itemVariants} className="mb-2">
              <span className="inline-block bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold mb-3">
                TRUSTED AUTOMOTIVE EXPERTS
              </span>
            </motion.div>
            <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Dealership Quality Repair at an Affordable Price
            </motion.h1>
            <motion.p variants={itemVariants} className="text-lg md:text-xl text-gray-200 mb-8">
              Specializing in transmission rebuilding, engine repair, and
              advanced diagnostics with over 38 years of combined experience.
            </motion.p>
            <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
              <motion.button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-semibold text-lg flex items-center" whileHover={{
              scale: 1.05
            }} whileTap={{
              scale: 0.95
            }} transition={{
              type: 'spring',
              stiffness: 400,
              damping: 17
            }}>
                Book Appointment <ArrowRightIcon className="ml-2 h-5 w-5" />
              </motion.button>
              <motion.button className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg" whileHover={{
              scale: 1.05
            }} whileTap={{
              scale: 0.95
            }} transition={{
              type: 'spring',
              stiffness: 400,
              damping: 17
            }}>
                View Cars for Sale
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </div>
      <div className="absolute bottom-0 left-0 w-full z-10">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full">
          <path fill="#f8fafc" fillOpacity="1" d="M0,128L80,149.3C160,171,320,213,480,213.3C640,213,800,171,960,165.3C1120,160,1280,192,1360,208L1440,224L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z"></path>
        </svg>
      </div>
    </motion.section>;
};
export default HeroSection;